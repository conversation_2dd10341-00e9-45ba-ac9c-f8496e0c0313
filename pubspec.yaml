name: tegra_ecommerce_app
description: "A new Flutter project."

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ^3.6.1

dependencies:
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  cupertino_icons: ^1.0.8
  dio: ^5.7.0
  easy_localization: ^3.0.7
  flutter_bloc: ^9.0.0
  flutter_easyloading: ^3.0.5
  flutter_phoenix: ^1.1.1
  flutter_rating_bar: ^4.0.1
  flutter_screenutil: ^5.9.3
  flutter_secure_storage: ^9.2.4
  flutter_svg: ^2.0.17

  fluttertoast: ^8.2.10
  freezed: ^2.5.8
  freezed_annotation: ^2.4.4
  get_it: ^8.0.3

  flutter:
    sdk: flutter

  google_fonts: ^6.2.1
  image_picker: ^1.1.2
  json_annotation: ^4.9.0
  loading_indicator: ^3.1.1
  lottie: ^3.3.1
  page_transition: ^2.2.1
  persistent_bottom_nav_bar: ^6.2.1
  pinput: ^5.0.1
  pretty_dio_logger: ^1.4.0
  shared_preferences: ^2.3.5
  skeletonizer: 1.4.2
  smooth_page_indicator: ^1.2.0+3
  pull_to_refresh_flutter3: ^2.0.2
  flutter_spinkit: ^5.2.1
  url_launcher: ^6.2.5

  device_preview: ^1.2.0
  iconsax: ^0.0.8
  firebase_core: any
  firebase_messaging: ^15.2.7
  flutter_local_notifications: ^19.2.1
  top_snackbar_flutter: ^3.3.0

dev_dependencies:
  build_runner: ^2.4.14
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  json_serializable: ^6.9.3

flutter:
  uses-material-design: true
  assets:
    - assets/images/pngs/
    - assets/images/svgs/
    - assets/languages/

  fonts:
    # FontWight
    - family: DINNextLTArabic
      fonts:
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Black-2.ttf
          weight: 900
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Black-3.ttf
          weight: 900
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Black-4.ttf
          weight: 900
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Bold-2.ttf
          weight: 700
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Bold-3.ttf
          weight: 700
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Bold-4.ttf
          weight: 700
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Heavy2-1.ttf
          weight: 800
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Heavy2-2.ttf
          weight: 800
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Heavy-1.ttf
          weight: 800
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTARABIC-LIGHT-1.ttf
          weight: 300
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Medium-2.ttf
          weight: 500
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Medium-3.ttf
          weight: 500
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Medium-4.ttf
          weight: 500
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Regular-2.ttf
          weight: 400
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Regular-3.ttf
          weight: 400
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Regular-4.ttf
          weight: 400
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-UltraLight-1.ttf
          weight: 200
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-UltraLight-2.ttf
          weight: 200
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-UltraLight-3.ttf
          weight: 200
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-UltraLight-4.ttf
          weight: 200

flutter_assets:
  assets_path: assets/images/
  output_path: lib/core/themes/
  filename: assets.dart

# use this command to run the package >> flutter pub run flutter_launcher_icons
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/pngs/app_logo.png"
  adaptive_icon_background: "#2aa69c"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/pngs/app_logo.png"
    background_color: "#2aa69c"
    theme_color: "#2aa69c"
  windows:
    generate: true
    image_path: "assets/images/pngs/app_logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/pngs/app_logo.png"

scripts:
  my_custom_script:
    script: flutter pub get
  build_runner_watch: flutter pub run build_runner watch
  build_runner_build: flutter pub run build_runner build
